import Constants from 'expo-constants';
import { Stack } from "expo-router";
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import {
  MD3LightTheme as DefaultTheme,
  PaperProvider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#155E95',
    secondary: '#80D8C3',
  }
}

export default function RootLayout() {
  return (
    <PaperProvider theme={theme} >
      <SafeAreaView style={{ flex: 1 }}>
        <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: Constants.statusBarHeight,
        backgroundColor: theme.colors.primary,
        zIndex: 1
      }} />
      <StatusBar style="light" />
      <Stack screenOptions={{ headerShown: false }} />
      </SafeAreaView>
    </PaperProvider>
  );
}
