import DynamicTable from "@/components/table";
import { useState } from "react";
import { Text, View } from "react-native";
import { Button, HelperText, TextInput } from "react-native-paper";

export default function Index() {
  
  const handleSubmit = async (data: any) => {
    console.log('Form submitted:', data);
    // Handle form submission
  };

  return (
    <View style={{ padding: 16, gap: 16 }}>
      <DynamicTable
        onSubmit={handleSubmit}
        showProgress={true}
        allowNavigation={true}
        submitButtonText="Submit Form"
        nextButtonText="Continue"
        backButtonText="Previous"
      />
    </View>
  );
}
