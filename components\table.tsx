import React, { useC<PERSON>back, useMemo, useRef, useState } from 'react';
import formTemplateData from '../assets/forms/household.json';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type FieldType = 'TEXT' | 'NUMBER' | 'EMAIL' | 'PASSWORD' | 'TEXTAREA' | 'SELECT' | 'RADIO' | 'CHECKBOX' | 'DATE' | 'TIME' | 'FILE';

export interface FormField {
  id: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
  description?: string;
  disabled?: boolean;
  defaultValue?: string | number | boolean;
}

export interface FormSection {
  name: string;
  description?: string;
  fields: FormField[];
}

export interface FormTemplate {
  formTemplateId: string;
  category: string;
  version: number;
  sections: FormSection[];
}

export interface FormData {
  [fieldId: string]: string | number | boolean | File | null;
}

export interface FormErrors {
  [fieldId: string]: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormErrors;
}

export interface DynamicTableProps {
  template?: FormTemplate;
  initialData?: FormData;
  onSubmit?: (data: FormData) => Promise<void> | void;
  onStepChange?: (step: number, totalSteps: number) => void;
  onDataChange?: (data: FormData) => void;
  className?: string;
  showProgress?: boolean;
  allowNavigation?: boolean;
  submitButtonText?: string;
  nextButtonText?: string;
  backButtonText?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

// ============================================================================
// ERROR BOUNDARY COMPONENT
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class FormErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Form Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className="error-boundary p-6 bg-red-50 border border-red-200 rounded-lg"
          role="alert"
          aria-live="assertive"
        >
          <h2 className="text-lg font-semibold text-red-800 mb-2">
            Something went wrong with the form
          </h2>
          <p className="text-red-600 mb-4">
            {this.state.error?.message || 'An unexpected error occurred'}
          </p>
          <button
            onClick={() => this.setState({ hasError: false })}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

const validateField = (field: FormField, value: string | number | boolean | File | null): string | null => {
  // Required field validation
  if (field.required && (value === null || value === undefined || value === '')) {
    return `${field.label} is required`;
  }

  // Skip validation if field is empty and not required
  if (!field.required && (value === null || value === undefined || value === '')) {
    return null;
  }

  const stringValue = String(value);

  // Type-specific validation
  switch (field.type) {
    case 'EMAIL':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(stringValue)) {
        return 'Please enter a valid email address';
      }
      break;

    case 'NUMBER':
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return 'Please enter a valid number';
      }
      if (field.validation?.min !== undefined && numValue < field.validation.min) {
        return `Value must be at least ${field.validation.min}`;
      }
      if (field.validation?.max !== undefined && numValue > field.validation.max) {
        return `Value must be at most ${field.validation.max}`;
      }
      break;

    case 'TEXT':
    case 'TEXTAREA':
    case 'PASSWORD':
      if (field.validation?.minLength && stringValue.length < field.validation.minLength) {
        return `Must be at least ${field.validation.minLength} characters`;
      }
      if (field.validation?.maxLength && stringValue.length > field.validation.maxLength) {
        return `Must be at most ${field.validation.maxLength} characters`;
      }
      if (field.validation?.pattern) {
        const regex = new RegExp(field.validation.pattern);
        if (!regex.test(stringValue)) {
          return 'Please enter a valid format';
        }
      }
      break;
  }

  return null;
};

const validateForm = (fields: FormField[], formData: FormData): ValidationResult => {
  const errors: FormErrors = {};
  let isValid = true;

  fields.forEach(field => {
    const error = validateField(field, formData[field.id]);
    if (error) {
      errors[field.id] = error;
      isValid = false;
    }
  });

  return { isValid, errors };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

const generateId = (prefix: string = 'field'): string => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

// ============================================================================
// FIELD COMPONENTS
// ============================================================================

interface FieldProps {
  field: FormField;
  value: string | number | boolean | File | null;
  error?: string;
  onChange: (value: string | number | boolean | File | null) => void;
  onBlur?: () => void;
  disabled?: boolean;
}

const TextInput: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  const inputId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${inputId}-error`, [inputId]);
  const descriptionId = useMemo(() => `${inputId}-description`, [inputId]);

  return (
    <div className="mb-4">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {field.label}
        {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </label>

      {field.description && (
        <p id={descriptionId} className="text-sm text-gray-500 mb-2">
          {field.description}
        </p>
      )}

      <input
        id={inputId}
        type={field.type.toLowerCase()}
        value={String(value || '')}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder={field.placeholder}
        disabled={disabled || field.disabled}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500
          ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300'}
          ${disabled || field.disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
        `}
        aria-invalid={!!error}
        aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        minLength={field.validation?.minLength}
        maxLength={field.validation?.maxLength}
        pattern={field.validation?.pattern}
      />

      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

const NumberInput: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  const inputId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${inputId}-error`, [inputId]);
  const descriptionId = useMemo(() => `${inputId}-description`, [inputId]);

  return (
    <div className="mb-4">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {field.label}
        {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </label>

      {field.description && (
        <p id={descriptionId} className="text-sm text-gray-500 mb-2">
          {field.description}
        </p>
      )}

      <input
        id={inputId}
        type="number"
        value={value === null || value === undefined ? '' : String(value)}
        onChange={(e) => onChange(e.target.value === '' ? null : Number(e.target.value))}
        onBlur={onBlur}
        placeholder={field.placeholder}
        disabled={disabled || field.disabled}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500
          ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300'}
          ${disabled || field.disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
        `}
        aria-invalid={!!error}
        aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        min={field.validation?.min}
        max={field.validation?.max}
      />

      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

const TextAreaInput: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  const inputId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${inputId}-error`, [inputId]);
  const descriptionId = useMemo(() => `${inputId}-description`, [inputId]);

  return (
    <div className="mb-4">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {field.label}
        {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </label>

      {field.description && (
        <p id={descriptionId} className="text-sm text-gray-500 mb-2">
          {field.description}
        </p>
      )}

      <textarea
        id={inputId}
        value={String(value || '')}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder={field.placeholder}
        disabled={disabled || field.disabled}
        rows={4}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical
          ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300'}
          ${disabled || field.disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
        `}
        aria-invalid={!!error}
        aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        minLength={field.validation?.minLength}
        maxLength={field.validation?.maxLength}
      />

      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

const SelectInput: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  const inputId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${inputId}-error`, [inputId]);
  const descriptionId = useMemo(() => `${inputId}-description`, [inputId]);

  return (
    <div className="mb-4">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {field.label}
        {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </label>

      {field.description && (
        <p id={descriptionId} className="text-sm text-gray-500 mb-2">
          {field.description}
        </p>
      )}

      <select
        id={inputId}
        value={String(value || '')}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        disabled={disabled || field.disabled}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500
          ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300'}
          ${disabled || field.disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
        `}
        aria-invalid={!!error}
        aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
      >
        <option value="">Select an option...</option>
        {field.options?.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>

      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

const RadioInput: React.FC<FieldProps> = ({ field, value, error, onChange, disabled }) => {
  const groupId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${groupId}-error`, [groupId]);
  const descriptionId = useMemo(() => `${groupId}-description`, [groupId]);

  return (
    <fieldset className="mb-4">
      <legend className="block text-sm font-medium text-gray-700 mb-2">
        {field.label}
        {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
      </legend>

      {field.description && (
        <p id={descriptionId} className="text-sm text-gray-500 mb-3">
          {field.description}
        </p>
      )}

      <div
        role="radiogroup"
        aria-labelledby={groupId}
        aria-invalid={!!error}
        aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        className="space-y-2"
      >
        {field.options?.map((option) => {
          const optionId = `${groupId}-${option}`;
          return (
            <label key={option} htmlFor={optionId} className="flex items-center">
              <input
                id={optionId}
                type="radio"
                name={groupId}
                value={option}
                checked={value === option}
                onChange={(e) => onChange(e.target.value)}
                disabled={disabled || field.disabled}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700">{option}</span>
            </label>
          );
        })}
      </div>

      {error && (
        <p id={errorId} className="mt-2 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </fieldset>
  );
};

const CheckboxInput: React.FC<FieldProps> = ({ field, value, error, onChange, disabled }) => {
  const inputId = useMemo(() => generateId(field.id), [field.id]);
  const errorId = useMemo(() => `${inputId}-error`, [inputId]);
  const descriptionId = useMemo(() => `${inputId}-description`, [inputId]);

  return (
    <div className="mb-4">
      <div className="flex items-start">
        <input
          id={inputId}
          type="checkbox"
          checked={Boolean(value)}
          onChange={(e) => onChange(e.target.checked)}
          disabled={disabled || field.disabled}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
          aria-invalid={!!error}
          aria-describedby={`${field.description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        />
        <div className="ml-3">
          <label htmlFor={inputId} className="text-sm font-medium text-gray-700">
            {field.label}
            {field.required && <span className="text-red-500 ml-1" aria-label="required">*</span>}
          </label>

          {field.description && (
            <p id={descriptionId} className="text-sm text-gray-500 mt-1">
              {field.description}
            </p>
          )}
        </div>
      </div>

      {error && (
        <p id={errorId} className="mt-2 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

// ============================================================================
// FIELD RENDERER COMPONENT
// ============================================================================

interface FieldRendererProps {
  field: FormField;
  value: string | number | boolean | File | null;
  error?: string;
  onChange: (value: string | number | boolean | File | null) => void;
  onBlur?: () => void;
  disabled?: boolean;
}

const FieldRenderer: React.FC<FieldRendererProps> = (props) => {
  const { field } = props;

  switch (field.type) {
    case 'TEXT':
    case 'EMAIL':
    case 'PASSWORD':
      return <TextInput {...props} />;
    case 'NUMBER':
      return <NumberInput {...props} />;
    case 'TEXTAREA':
      return <TextAreaInput {...props} />;
    case 'SELECT':
      return <SelectInput {...props} />;
    case 'RADIO':
      return <RadioInput {...props} />;
    case 'CHECKBOX':
      return <CheckboxInput {...props} />;
    default:
      return (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            Unsupported field type: {field.type}
          </p>
        </div>
      );
  }
};

// ============================================================================
// MAIN DYNAMIC TABLE COMPONENT
// ============================================================================

const DynamicTable: React.FC<DynamicTableProps> = ({
  template,
  initialData = {},
  onSubmit,
  onStepChange,
  onDataChange,
  className = '',
  showProgress = true,
  allowNavigation = true,
  submitButtonText = 'Submit',
  nextButtonText = 'Next',
  backButtonText = 'Back',
  isLoading = false,
  disabled = false,
}) => {
  // Use provided template or default to imported data
  const formTemplate: FormTemplate = template || (formTemplateData as FormTemplate);

  // State management
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Refs for accessibility
  const formRef = useRef<HTMLFormElement>(null);
  const errorSummaryRef = useRef<HTMLDivElement>(null);

  // Memoized values
  const currentSection = useMemo(() => formTemplate.sections[currentStep], [formTemplate.sections, currentStep]);
  const totalSteps = useMemo(() => formTemplate.sections.length, [formTemplate.sections.length]);
  const isLastStep = useMemo(() => currentStep === totalSteps - 1, [currentStep, totalSteps]);
  const isFirstStep = useMemo(() => currentStep === 0, [currentStep]);

  // Debounced validation
  const debouncedValidation = useCallback(
    debounce((fields: FormField[], data: FormData) => {
      const result = validateForm(fields, data);
      setErrors(result.errors);
    }, 300),
    []
  );

  // Handle field changes
  const handleFieldChange = useCallback((fieldId: string, value: string | number | boolean | File | null) => {
    setFormData(prev => {
      const newData = { ...prev, [fieldId]: value };
      onDataChange?.(newData);

      // Validate only if field has been touched
      if (touchedFields.has(fieldId)) {
        debouncedValidation(currentSection.fields, newData);
      }

      return newData;
    });
  }, [currentSection.fields, debouncedValidation, onDataChange, touchedFields]);

  // Handle field blur (mark as touched)
  const handleFieldBlur = useCallback((fieldId: string) => {
    setTouchedFields(prev => new Set([...prev, fieldId]));

    // Validate this specific field
    const field = currentSection.fields.find(f => f.id === fieldId);
    if (field) {
      const error = validateField(field, formData[fieldId]);
      setErrors(prev => {
        const newErrors = { ...prev };
        if (error) {
          newErrors[fieldId] = error;
        } else {
          delete newErrors[fieldId];
        }
        return newErrors;
      });
    }
  }, [currentSection.fields, formData]);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const result = validateForm(currentSection.fields, formData);
    setErrors(result.errors);

    // Focus first error field
    if (!result.isValid && errorSummaryRef.current) {
      errorSummaryRef.current.focus();
    }

    return result.isValid;
  }, [currentSection.fields, formData]);

  // Handle navigation
  const handleNext = useCallback(async () => {
    if (!validateCurrentStep()) {
      return;
    }

    if (isLastStep) {
      // Submit form
      setIsSubmitting(true);
      try {
        await onSubmit?.(formData);
      } catch (error) {
        console.error('Form submission error:', error);
        // Handle submission error
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Go to next step
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      onStepChange?.(nextStep, totalSteps);

      // Clear errors for next step
      setErrors({});

      // Scroll to top
      formRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [validateCurrentStep, isLastStep, onSubmit, formData, currentStep, totalSteps, onStepChange]);

  const handleBack = useCallback(() => {
    if (!isFirstStep && allowNavigation) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      onStepChange?.(prevStep, totalSteps);

      // Clear errors
      setErrors({});

      // Scroll to top
      formRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isFirstStep, allowNavigation, currentStep, totalSteps, onStepChange]);

  // Keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleNext();
    }
  }, [handleNext]);

  // Error summary for accessibility
  const errorFields = useMemo(() => {
    return Object.entries(errors).filter(([, error]) => error);
  }, [errors]);

  const hasErrors = errorFields.length > 0;

  return (
    <FormErrorBoundary>
      <div className={`dynamic-table max-w-4xl mx-auto p-6 ${className}`}>
        {/* Progress indicator */}
        {showProgress && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Step {currentStep + 1} of {totalSteps}
              </span>
              <span className="text-sm text-gray-500">
                {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
                role="progressbar"
                aria-valuenow={currentStep + 1}
                aria-valuemin={1}
                aria-valuemax={totalSteps}
                aria-label={`Step ${currentStep + 1} of ${totalSteps}`}
              />
            </div>
          </div>
        )}

        {/* Error summary for accessibility */}
        {hasErrors && (
          <div
            ref={errorSummaryRef}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md"
            role="alert"
            aria-live="polite"
            tabIndex={-1}
          >
            <h3 className="text-sm font-medium text-red-800 mb-2">
              Please correct the following errors:
            </h3>
            <ul className="text-sm text-red-700 space-y-1">
              {errorFields.map(([fieldId, error]) => {
                const field = currentSection.fields.find(f => f.id === fieldId);
                return (
                  <li key={fieldId}>
                    <a
                      href={`#${fieldId}`}
                      className="underline hover:no-underline"
                      onClick={(e) => {
                        e.preventDefault();
                        document.getElementById(fieldId)?.focus();
                      }}
                    >
                      {field?.label}: {error}
                    </a>
                  </li>
                );
              })}
            </ul>
          </div>
        )}

        {/* Form */}
        <form
          ref={formRef}
          onSubmit={(e) => {
            e.preventDefault();
            handleNext();
          }}
          onKeyDown={handleKeyDown}
          noValidate
        >
          {/* Section header */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {currentSection.name}
            </h2>
            {currentSection.description && (
              <p className="text-gray-600">{currentSection.description}</p>
            )}
          </div>

          {/* Fields */}
          <div className="space-y-6">
            {currentSection.fields.map((field) => (
              <FieldRenderer
                key={field.id}
                field={field}
                value={formData[field.id] || null}
                error={errors[field.id]}
                onChange={(value) => handleFieldChange(field.id, value)}
                onBlur={() => handleFieldBlur(field.id)}
                disabled={disabled || isLoading || isSubmitting}
              />
            ))}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <div>
              {!isFirstStep && allowNavigation && (
                <button
                  type="button"
                  onClick={handleBack}
                  disabled={disabled || isLoading || isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {backButtonText}
                </button>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {isLoading || isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-sm text-gray-600">
                    {isSubmitting ? 'Submitting...' : 'Loading...'}
                  </span>
                </div>
              ) : null}

              <button
                type="submit"
                disabled={disabled || isLoading || isSubmitting}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLastStep ? submitButtonText : nextButtonText}
              </button>
            </div>
          </div>
        </form>

        {/* Keyboard shortcuts help */}
        <div className="mt-6 text-xs text-gray-500 text-center">
          Press Ctrl+Enter to {isLastStep ? 'submit' : 'continue to next step'}
        </div>
      </div>
    </FormErrorBoundary>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

export default DynamicTable;
export { FieldRenderer, FormErrorBoundary, validateField, validateForm };
