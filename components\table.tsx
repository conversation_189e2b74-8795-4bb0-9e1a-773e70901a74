import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  View
} from 'react-native';
import {
  ActivityIndicator,
  Button,
  Checkbox,
  HelperText,
  TextInput as PaperTextInput,
  RadioButton,
  Text
} from 'react-native-paper';
import { Dropdown } from 'react-native-paper-dropdown';

// Import form template
let formTemplateData;
try {
  formTemplateData = require('../assets/forms/household.json');
} catch (error) {
  console.warn('Could not load form template, using fallback:', error);
  // Fallback form template for testing
  formTemplateData = {
    formTemplateId: "test-form",
    category: "Test",
    version: 1,
    sections: [
      {
        name: "Test Section",
        description: "This is a test section",
        fields: [
          {
            id: "test-text",
            label: "Test Text Field",
            type: "TEXT",
            required: true,
            placeholder: "Enter some text"
          },
          {
            id: "test-number",
            label: "Test Number Field",
            type: "NUMBER",
            required: false,
            placeholder: "Enter a number"
          }
        ]
      }
    ]
  };
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type FieldType = 'TEXT' | 'NUMBER' | 'EMAIL' | 'PASSWORD' | 'TEXTAREA' | 'SELECT' | 'RADIO' | 'CHECKBOX' | 'DATE' | 'TIME';

export interface FormField {
  id: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
  description?: string;
  disabled?: boolean;
  defaultValue?: string | number | boolean;
}

export interface FormSection {
  name: string;
  description?: string;
  fields: FormField[];
}

export interface FormTemplate {
  formTemplateId: string;
  category: string;
  title: string;
  version: number;
  sections: FormSection[];
}

export interface FormData {
  [fieldId: string]: string | number | boolean | null;
}

export interface FormErrors {
  [fieldId: string]: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormErrors;
}

export interface DynamicTableProps {
  template?: FormTemplate;
  initialData?: FormData;
  onSubmit?: (data: FormData) => Promise<void> | void;
  onStepChange?: (step: number, totalSteps: number) => void;
  onDataChange?: (data: FormData) => void;
  style?: any;
  showProgress?: boolean;
  allowNavigation?: boolean;
  submitButtonText?: string;
  nextButtonText?: string;
  backButtonText?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

// ============================================================================
// ERROR BOUNDARY COMPONENT
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class FormErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Form Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View
          style={{
            padding: 24,
            backgroundColor: '#FEF2F2',
            borderColor: '#FECACA',
            borderWidth: 1,
            borderRadius: 8,
            margin: 16,
          }}
          accessibilityRole="alert"
          accessibilityLiveRegion="assertive"
        >
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: '#991B1B',
            marginBottom: 8,
          }}>
            Something went wrong with the form
          </Text>
          <Text style={{
            color: '#B91C1C',
            marginBottom: 16,
          }}>
            {this.state.error?.message || 'An unexpected error occurred'}
          </Text>
          <Button
            mode="contained"
            onPress={() => this.setState({ hasError: false })}
            buttonColor="#DC2626"
            textColor="#FFFFFF"
          >
            Try Again
          </Button>
        </View>
      );
    }

    return this.props.children;
  }
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

const validateField = (field: FormField, value: string | number | boolean | null): string | null => {
  // Required field validation
  if (field.required && (value === null || value === undefined || value === '')) {
    return `${field.label} is required`;
  }

  // Skip validation if field is empty and not required
  if (!field.required && (value === null || value === undefined || value === '')) {
    return null;
  }

  const stringValue = String(value);

  // Type-specific validation
  switch (field.type) {
    case 'EMAIL':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(stringValue)) {
        return 'Please enter a valid email address';
      }
      break;

    case 'NUMBER':
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return 'Please enter a valid number';
      }
      if (field.validation?.min !== undefined && numValue < field.validation.min) {
        return `Value must be at least ${field.validation.min}`;
      }
      if (field.validation?.max !== undefined && numValue > field.validation.max) {
        return `Value must be at most ${field.validation.max}`;
      }
      break;

    case 'TEXT':
    case 'TEXTAREA':
    case 'PASSWORD':
      if (field.validation?.minLength && stringValue.length < field.validation.minLength) {
        return `Must be at least ${field.validation.minLength} characters`;
      }
      if (field.validation?.maxLength && stringValue.length > field.validation.maxLength) {
        return `Must be at most ${field.validation.maxLength} characters`;
      }
      if (field.validation?.pattern) {
        const regex = new RegExp(field.validation.pattern);
        if (!regex.test(stringValue)) {
          return 'Please enter a valid format';
        }
      }
      break;
  }

  return null;
};

const validateForm = (fields: FormField[], formData: FormData): ValidationResult => {
  const errors: FormErrors = {};
  let isValid = true;

  fields.forEach(field => {
    const error = validateField(field, formData[field.id]);
    if (error) {
      errors[field.id] = error;
      isValid = false;
    }
  });

  return { isValid, errors };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

const generateId = (prefix: string = 'field'): string => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

// ============================================================================
// FIELD COMPONENTS
// ============================================================================

interface FieldProps {
  field: FormField;
  value: string | number | boolean | null;
  error?: string;
  onChange: (value: string | number | boolean | null) => void;
  onBlur?: () => void;
  disabled?: boolean;
}

const TextInputField: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  console.log('TextInputField rendering:', field.label, value);

  return (
    <View style={styles.fieldContainer}>
      <PaperTextInput
        label={field.label + (field.required ? ' *' : '')}
        value={String(value || '')}
        onChangeText={(text) => onChange(text)}
        onBlur={onBlur}
        placeholder={field.placeholder}
        disabled={disabled || field.disabled}
        error={!!error}
        mode="outlined"
        style={styles.textInput}
        keyboardType={field.type === 'EMAIL' ? 'email-address' : 'default'}
        secureTextEntry={field.type === 'PASSWORD'}
        multiline={field.type === 'TEXTAREA'}
        numberOfLines={field.type === 'TEXTAREA' ? 4 : 1}
        accessibilityLabel={field.label}
        accessibilityHint={field.description}
        accessibilityRole="text"
        maxLength={field.validation?.maxLength}
      />

      {field.description && (
        <HelperText type="info" style={styles.helperText}>
          {field.description}
        </HelperText>
      )}

      {error && (
        <HelperText type="error" visible={!!error} style={styles.errorText}>
          {error}
        </HelperText>
      )}
    </View>
  );
};

const NumberInputField: React.FC<FieldProps> = ({ field, value, error, onChange, onBlur, disabled }) => {
  return (
    <View style={styles.fieldContainer}>
      <PaperTextInput
        label={field.label + (field.required ? ' *' : '')}
        value={value === null || value === undefined ? '' : String(value)}
        onChangeText={(text: string) => onChange(text === '' ? null : Number(text))}
        onBlur={onBlur}
        placeholder={field.placeholder}
        disabled={disabled || field.disabled}
        error={!!error}
        mode="outlined"
        style={styles.textInput}
        keyboardType="numeric"
        accessibilityLabel={field.label}
        accessibilityHint={field.description}
        accessibilityRole="text"
      />

      {field.description && (
        <HelperText type="info" style={styles.helperText}>
          {field.description}
        </HelperText>
      )}

      {error && (
        <HelperText type="error" visible={!!error} style={styles.errorText}>
          {error}
        </HelperText>
      )}
    </View>
  );
};

const SelectInputField: React.FC<FieldProps> = ({ field, value, error, onChange, disabled }) => {
  return (
    <View style={styles.fieldContainer}>
      <Text style={styles.radioLabel}>
        {field.label}{field.required ? ' *' : ''}
      </Text>

      {field.description && (
        <HelperText type="info" style={styles.helperText}>
          {field.description}
        </HelperText>
      )}

      <Dropdown
        label="Select an option..."
        value={String(value || '')}
        onSelect={(selectedValue?: string) => onChange(selectedValue || '')}
        options={field.options?.map(option => ({ label: option, value: option })) || []}
        disabled={disabled || field.disabled}
        mode="outlined"
      />

      {error && (
        <HelperText type="error" visible={!!error} style={styles.errorText}>
          {error}
        </HelperText>
      )}
    </View>
  );
};

const RadioInputField: React.FC<FieldProps> = ({ field, value, error, onChange, disabled }) => {
  return (
    <View style={styles.radioContainer}>
      <Text style={styles.radioLabel}>
        {field.label}{field.required ? ' *' : ''}
      </Text>

      {field.description && (
        <HelperText type="info" style={styles.helperText}>
          {field.description}
        </HelperText>
      )}

      <RadioButton.Group
        onValueChange={(selectedValue: string) => onChange(selectedValue)}
        value={String(value || '')}
      >
        {field.options?.map((option) => (
          <View key={option} style={styles.radioOption}>
            <RadioButton
              value={option}
              disabled={disabled || field.disabled}
            />
            <Text style={styles.radioOptionText}>{option}</Text>
          </View>
        ))}
      </RadioButton.Group>

      {error && (
        <HelperText type="error" visible={!!error} style={styles.errorText}>
          {error}
        </HelperText>
      )}
    </View>
  );
};

const CheckboxInputField: React.FC<FieldProps> = ({ field, value, error, onChange, disabled }) => {
  return (
    <View style={styles.fieldContainer}>
      <View style={styles.checkboxContainer}>
        <Checkbox
          status={Boolean(value) ? 'checked' : 'unchecked'}
          onPress={() => onChange(!Boolean(value))}
          disabled={disabled || field.disabled}
        />
        <Text style={styles.checkboxLabel}>
          {field.label}{field.required ? ' *' : ''}
        </Text>
      </View>

      {field.description && (
        <HelperText type="info" style={styles.helperText}>
          {field.description}
        </HelperText>
      )}

      {error && (
        <HelperText type="error" visible={!!error} style={styles.errorText}>
          {error}
        </HelperText>
      )}
    </View>
  );
};











// ============================================================================
// FIELD RENDERER COMPONENT
// ============================================================================

interface FieldRendererProps {
  field: FormField;
  value: string | number | boolean | null;
  error?: string;
  onChange: (value: string | number | boolean | null) => void;
  onBlur?: () => void;
  disabled?: boolean;
}

const FieldRenderer: React.FC<FieldRendererProps> = (props) => {
  const { field } = props;

  console.log('FieldRenderer rendering:', field.type, field.label);

  switch (field.type) {
    case 'TEXT':
    case 'EMAIL':
    case 'PASSWORD':
    case 'TEXTAREA':
      return <TextInputField {...props} />;
    case 'NUMBER':
      return <NumberInputField {...props} />;
    case 'SELECT':
      return <SelectInputField {...props} />;
    case 'RADIO':
      return <RadioInputField {...props} />;
    case 'CHECKBOX':
      return <CheckboxInputField {...props} />;
    default:
      console.warn('Unsupported field type:', field.type);
      return (
        <View style={styles.unsupportedField}>
          <Text style={styles.unsupportedFieldText}>
            Unsupported field type: {field.type}
          </Text>
        </View>
      );
  }
};

// ============================================================================
// MAIN DYNAMIC TABLE COMPONENT
// ============================================================================

const DynamicTable: React.FC<DynamicTableProps> = ({
  template,
  initialData = {},
  onSubmit,
  onStepChange,
  onDataChange,
  style,
  showProgress = true,
  allowNavigation = true,
  submitButtonText = 'Submit',
  nextButtonText = 'Next',
  backButtonText = 'Back',
  isLoading = false,
  disabled = false,
}) => {
  // Use provided template or default to imported data
  const formTemplate: FormTemplate = template || (formTemplateData as FormTemplate);

  // State management
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Refs for accessibility
  const formRef = useRef<ScrollView>(null);
  const errorSummaryRef = useRef<View>(null);

  // Debug logging
  console.log('DynamicTable Debug:', {
    hasTemplate: !!formTemplate,
    currentStep,
    sectionsLength: formTemplate?.sections?.length,
    hasCurrentSection: !!formTemplate?.sections?.[currentStep],
    currentSectionName: formTemplate?.sections?.[currentStep]?.name,
    fieldsCount: formTemplate?.sections?.[currentStep]?.fields?.length,
  });

  // Memoized values with safety checks
  const currentSection = useMemo(() => {
    if (!formTemplate?.sections || formTemplate.sections.length === 0) {
      console.warn('No sections found in form template');
      return null;
    }
    return formTemplate.sections[currentStep];
  }, [formTemplate.sections, currentStep]);

  const totalSteps = useMemo(() => formTemplate?.sections?.length || 0, [formTemplate.sections]);
  const isLastStep = useMemo(() => currentStep === totalSteps - 1, [currentStep, totalSteps]);
  const isFirstStep = useMemo(() => currentStep === 0, [currentStep]);

  // Early return if no valid section
  if (!currentSection) {
    return (
      <FormErrorBoundary>
        <View style={styles.container}>
          <Text style={styles.sectionTitle}>No form data available</Text>
          <Text style={styles.sectionDescription}>
            Please check that the form template is properly loaded.
          </Text>
        </View>
      </FormErrorBoundary>
    );
  }

  // Debounced validation
  const debouncedValidation = useCallback(
    debounce((fields: FormField[], data: FormData) => {
      const result = validateForm(fields, data);
      setErrors(result.errors);
    }, 300),
    []
  );

  // Handle field changes
  const handleFieldChange = useCallback((fieldId: string, value: string | number | boolean | null) => {
    setFormData(prev => {
      const newData = { ...prev, [fieldId]: value };
      onDataChange?.(newData);

      // Validate only if field has been touched
      if (touchedFields.has(fieldId)) {
        debouncedValidation(currentSection.fields, newData);
      }

      return newData;
    });
  }, [currentSection.fields, debouncedValidation, onDataChange, touchedFields]);

  // Handle field blur (mark as touched)
  const handleFieldBlur = useCallback((fieldId: string) => {
    setTouchedFields(prev => new Set([...prev, fieldId]));

    // Validate this specific field
    const field = currentSection.fields.find(f => f.id === fieldId);
    if (field) {
      const error = validateField(field, formData[fieldId]);
      setErrors(prev => {
        const newErrors = { ...prev };
        if (error) {
          newErrors[fieldId] = error;
        } else {
          delete newErrors[fieldId];
        }
        return newErrors;
      });
    }
  }, [currentSection.fields, formData]);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const result = validateForm(currentSection.fields, formData);
    setErrors(result.errors);

    // Focus first error field
    if (!result.isValid && errorSummaryRef.current) {
      errorSummaryRef.current.focus();
    }

    return result.isValid;
  }, [currentSection.fields, formData]);

  // Handle navigation
  const handleNext = useCallback(async () => {
    if (!validateCurrentStep()) {
      return;
    }

    if (isLastStep) {
      // Submit form
      setIsSubmitting(true);
      try {
        await onSubmit?.(formData);
      } catch (error) {
        console.error('Form submission error:', error);
        // Handle submission error
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Go to next step
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      onStepChange?.(nextStep, totalSteps);

      // Clear errors for next step
      setErrors({});

      // Scroll to top
      formRef.current?.scrollTo({ y: 0, animated: true });
    }
  }, [validateCurrentStep, isLastStep, onSubmit, formData, currentStep, totalSteps, onStepChange]);

  const handleBack = useCallback(() => {
    if (!isFirstStep && allowNavigation) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      onStepChange?.(prevStep, totalSteps);

      // Clear errors
      setErrors({});

      // Scroll to top
      formRef.current?.scrollTo({ y: 0, animated: true });
    }
  }, [isFirstStep, allowNavigation, currentStep, totalSteps, onStepChange]);



  // Error summary for accessibility
  const errorFields = useMemo(() => {
    return Object.entries(errors).filter(([, error]) => error);
  }, [errors]);

  const hasErrors = errorFields.length > 0;

  return (
    <FormErrorBoundary>
      <ScrollView style={[styles.container, style]} contentContainerStyle={{ flexGrow: 1 }}>
        {formTemplate.title && (
          <Text style={styles.formTitle}>{formTemplate.title}</Text>
        )}
        {/* Progress indicator */}
        {showProgress && (
          <View style={styles.progressContainer}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text style={styles.progressText}>
                Step {currentStep + 1} of {totalSteps}
              </Text>
              <Text style={styles.progressPercentage}>
                {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
              </Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${((currentStep + 1) / totalSteps) * 100}%` }
                ]}
                accessibilityRole="progressbar"
                accessibilityValue={{
                  min: 1,
                  max: totalSteps,
                  now: currentStep + 1,
                }}
                accessibilityLabel={`Step ${currentStep + 1} of ${totalSteps}`}
              />
            </View>
          </View>
        )}

        {/* Error summary for accessibility */}
        {hasErrors && (
          <View
            ref={errorSummaryRef}
            style={styles.errorSummary}
            accessibilityRole="alert"
            accessibilityLiveRegion="polite"
          >
            <Text style={styles.errorSummaryTitle}>
              Please correct the following errors:
            </Text>
            {errorFields.map(([fieldId, error]) => {
              const field = currentSection.fields.find(f => f.id === fieldId);
              return (
                <Text key={fieldId} style={styles.errorSummaryText}>
                  • {field?.label}: {error}
                </Text>
              );
            })}
          </View>
        )}

        {/* Form */}
        <View>
          {/* Section header */}

          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {currentSection.name}
            </Text>
            {currentSection.description && (
              <Text style={styles.sectionDescription}>{currentSection.description}</Text>
            )}
          </View>

          {/* Fields */}
          <View>
            {currentSection.fields && currentSection.fields.length > 0 ? (
              currentSection.fields.map((field) => {
                console.log('Rendering field:', field);
                return (
                  <FieldRenderer
                    key={field.id}
                    field={field}
                    value={formData[field.id] || null}
                    error={errors[field.id]}
                    onChange={(value) => handleFieldChange(field.id, value)}
                    onBlur={() => handleFieldBlur(field.id)}
                    disabled={disabled || isLoading || isSubmitting}
                  />
                );
              })
            ) : (
              <Text style={styles.sectionDescription}>No fields available in this section</Text>
            )}
          </View>

          {/* Navigation buttons */}
          <View style={styles.navigationContainer}>
            <View style={styles.navigationLeft}>
              {!isFirstStep && allowNavigation && (
                <Button
                  mode="outlined"
                  onPress={handleBack}
                  disabled={disabled || isLoading || isSubmitting}
                  accessibilityLabel={backButtonText}
                  accessibilityRole="button"
                >
                  {backButtonText}
                </Button>
              )}
            </View>

            <View style={styles.navigationRight}>
              {isLoading || isSubmitting ? (
                <View style={[styles.loadingContainer, { marginRight: 12 }]}>
                  <ActivityIndicator size="small" />
                  <Text style={[styles.loadingText, { marginLeft: 8 }]}>
                    {isSubmitting ? 'Submitting...' : 'Loading...'}
                  </Text>
                </View>
              ) : null}

              <Button
                mode="contained"
                onPress={handleNext}
                disabled={disabled || isLoading || isSubmitting}
                accessibilityLabel={isLastStep ? submitButtonText : nextButtonText}
                accessibilityRole="button"
              >
                {isLastStep ? submitButtonText : nextButtonText}
              </Button>
            </View>
          </View>
        </View>

        {/* Keyboard shortcuts help */}
        <Text style={styles.keyboardHint}>
          Tap {isLastStep ? 'Submit' : 'Next'} to {isLastStep ? 'submit' : 'continue to next step'}
        </Text>
      </ScrollView>
    </FormErrorBoundary>
  );
};

// ============================================================================
// EXPORTS
// ============================================================================

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#ffffff',
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  progressPercentage: {
    fontSize: 12,
    color: '#6B7280',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginTop: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 4,
  },
  errorSummary: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  errorSummaryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#991B1B',
    marginBottom: 8,
  },
  errorSummaryText: {
    fontSize: 12,
    color: '#B91C1C',
    marginBottom: 4,
  },
  sectionHeader: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#6B7280',
  },
  fieldContainer: {
    marginBottom: 16,
  },
  textInput: {
    backgroundColor: '#ffffff',
  },
  helperText: {
    fontSize: 12,
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  radioContainer: {
    marginBottom: 16,
  },
  radioLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioOptionText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkboxLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginLeft: 12,
    flex: 1,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 32,
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  navigationLeft: {
    flex: 1,
  },
  navigationRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280',
  },
  keyboardHint: {
    textAlign: 'center',
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 16,
  },
  unsupportedField: {
    backgroundColor: '#FFFBEB',
    borderColor: '#FDE68A',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  unsupportedFieldText: {
    fontSize: 14,
    color: '#92400E',
  },
});

export default DynamicTable;
export { FieldRenderer, FormErrorBoundary, validateField, validateForm };

