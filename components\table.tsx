import React, { useState } from "react";
import { View, ScrollView } from "react-native";
import { Text, TextInput, RadioButton, Button, HelperText } from "react-native-paper";
import { Dropdown } from 'react-native-paper-dropdown';

const formTemplate = require('./../assets/forms/household.json'); // Load your JSON here

const DynamicForm = () => {
  const [step, setStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  const currentSection = formTemplate.sections[step];

  const handleChange = (fieldId, value) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));
  };

  const validateStep = () => {
    const newErrors = {};
    currentSection.fields.forEach(field => {
      if (field.required && !formData[field.id]) {
        newErrors[field.id] = 'This field is required';
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep()) {
      if (step < formTemplate.sections.length - 1) {
        setStep(step + 1);
      } else {
        console.log("Form submitted:", formData);
        // Submit logic here
      }
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const renderField = (field) => {
    const value = formData[field.id] || '';
    const error = errors[field.id];

    switch (field.type) {
      case "TEXT":
        return (
          <TextInput
            label={field.label}
            value={value}
            onChangeText={(text) => handleChange(field.id, text)}
            error={!!error}
            style={{ marginBottom: 16 }}
          />
        );

      case "NUMBER":
        return (
          <TextInput
            label={field.label}
            value={value.toString()}
            keyboardType="numeric"
            onChangeText={(text) => handleChange(field.id, text)}
            error={!!error}
            style={{ marginBottom: 16 }}
          />
        );

      case "RADIO":
        return (
          <View style={{ marginBottom: 16 }}>
            <Text>{field.label}</Text>
            <RadioButton.Group
              onValueChange={(val) => handleChange(field.id, val)}
              value={value}
            >
              {field.options.map((option) => (
                <RadioButton.Item key={option} label={option} value={option} />
              ))}
            </RadioButton.Group>
            {error && <HelperText type="error">{error}</HelperText>}
          </View>
        );

      case "SELECT":
        return (
          <Dropdown
            label={field.label}
            value={value}
            mode="outlined"
            onFocus={() => {}}
            onChangeText={(val) => handleChange(field.id, val)}
            error={!!error}
            style={{ marginBottom: 16 }}
          />
        );

      default:
        return null;
    }
  };

  return (
    <ScrollView contentContainerStyle={{ padding: 16 }}>
      <Text variant="titleLarge" style={{ marginBottom: 12 }}>
        {currentSection.name}
      </Text>

      {currentSection.fields.map((field) => (
        <View key={field.id}>
          {renderField(field)}
        </View>
      ))}

      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 24 }}>
        {step > 0 && (
          <Button mode="outlined" onPress={handleBack}>
            Back
          </Button>
        )}
        <Button mode="contained" onPress={handleNext}>
          {step < formTemplate.sections.length - 1 ? 'Next' : 'Submit'}
        </Button>
      </View>
    </ScrollView>
  );
};

export default DynamicForm;